# -*- mode: python ; coding: utf-8 -*-

import os
from datetime import datetime
from PyInstaller.utils.win32.versioninfo import (
    FixedFileInfo,
    StringFileInfo,
    StringTable,
    StringStruct,
    VarFileInfo,
    VarStruct,
    VSVersionInfo,
)

block_cipher = None

# 版本信息
VERSION = "1.0.1"
COMPANY_NAME = "MSBuild Tool"
PRODUCT_NAME = f"MSBuild快速编译工具 v{VERSION}"
FILE_DESCRIPTION = "MSBuild项目快速编译工具"
INTERNAL_NAME = "MSBuildTool"
ORIGINAL_FILENAME = f"MSBuildTool_v{VERSION}.exe"
YEAR = datetime.now().year

version_info = VSVersionInfo(
    ffi=FixedFileInfo(
        filevers=(1, 0, 0, 0),
        prodvers=(1, 0, 0, 0),
        mask=0x3f,
        flags=0x0,
        OS=0x40004,
        fileType=0x1,
        subtype=0x0,
        date=(0, 0)
    ),
    kids=[
        StringFileInfo([
            StringTable(
                "080404b0",
                [
                    StringStruct("CompanyName", COMPANY_NAME),
                    StringStruct("FileDescription", FILE_DESCRIPTION),
                    StringStruct("FileVersion", VERSION),
                    StringStruct("InternalName", INTERNAL_NAME),
                    StringStruct("LegalCopyright", f"Copyright (C) {YEAR}"),
                    StringStruct("OriginalFilename", ORIGINAL_FILENAME),
                    StringStruct("ProductName", PRODUCT_NAME),
                    StringStruct("ProductVersion", VERSION)
                ])
        ]),
        VarFileInfo([VarStruct("Translation", [2052, 1200])])
    ]
)

a = Analysis(
    ['msbuild.py'],
    pathex=[],
    binaries=[],
    datas=[], 
    hiddenimports=[],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name=ORIGINAL_FILENAME.replace('.exe', ''),
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    version=version_info,
) 