@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ========================================
echo MSBuild工具自动构建脚本
echo ========================================

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: Python未安装或不在PATH中
    pause
    exit /b 1
)

:: 显示菜单
:menu
echo.
echo 请选择构建类型:
echo 1. Patch版本 (x.x.X+1) - 默认
echo 2. Minor版本 (x.X+1.0)
echo 3. Major版本 (X+1.0.0)
echo 4. 不增加版本号，直接构建
echo 5. 仅显示当前版本
echo 6. 退出
echo.

set /p choice="请输入选择 (1-6): "

if "%choice%"=="1" (
    python auto_build.py --type patch
    goto end
)
if "%choice%"=="2" (
    python auto_build.py --type minor
    goto end
)
if "%choice%"=="3" (
    python auto_build.py --type major
    goto end
)
if "%choice%"=="4" (
    python auto_build.py --no-version-bump
    goto end
)
if "%choice%"=="5" (
    python version_manager.py --show
    goto menu
)
if "%choice%"=="6" (
    exit /b 0
)

echo 无效选择，请重新输入
goto menu

:end
echo.
echo 构建完成!
pause
